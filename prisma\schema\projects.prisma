model projects {
  id             String              @id @default(uuid()) @db.Uuid
  name           String
  contact_name   String?
  contact_phone  String?
  contact_email  String?
  budget         Float?
  ministry_id    String?             @db.Uuid
  department_id  String?             @db.Uuid
  division_id    String?             @db.Uuid
  created_at     DateTime            @default(now())
  updated_at     DateTime            @default(now()) @updatedAt
  deleted_at     DateTime?
  created_by_id  String?             @db.Uuid
  deleted_by_id  String?             @db.Uuid
  updated_by_id  String?             @db.Uuid
  account_name   String              @default("")
  account_id     String              @default("")
  code           String              @unique
  provider_type  ProjectProviderType @default(HWC)
  psa_email      String              @default("")
  status         ProjectStatus       @default(DRAFT)
  project_usages project_usages[]
  creator        users?              @relation("ProjectCreator", fields: [created_by_id], references: [id])
  deleter        users?              @relation("ProjectDeleter", fields: [deleted_by_id], references: [id])
  department     departments?        @relation(fields: [department_id], references: [id])
  division       divisions?          @relation(fields: [division_id], references: [id])
  ministry       ministries?         @relation(fields: [ministry_id], references: [id])
  updater        users?              @relation("ProjectUpdater", fields: [updated_by_id], references: [id])

  @@index([name, ministry_id, department_id, division_id, code, status])
}

enum ProjectStatus {
  DRAFT
  ACTIVE
  CLOSED
}

enum ProjectProviderType {
  HWC
  AWS
  CHM
}
