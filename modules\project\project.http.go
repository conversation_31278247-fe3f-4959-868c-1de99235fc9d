package project

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/csp/csp-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewProjectHTTP(e *echo.Echo) {
	project := &ProjectController{}

	// Project routes - all require authentication
	e.GET("/projects", core.WithHTTPContext(project.Pagination), middleware.AuthMiddleware())
	e.GET("/projects/:id", core.WithHTTPContext(project.Find), middleware.AuthMiddleware())
	e.GET("/projects/:id/usages", core.WithHTTPContext(project.GetUsages), middleware.AuthMiddleware())
	e.POST("/projects", core.WithHTTPContext(project.Create), middleware.AuthMiddleware())
	e.PUT("/projects/:id", core.WithHTTPContext(project.Update), middleware.AuthMiddleware())
	e.DELETE("/projects/:id", core.WithHTTPContext(project.Delete), middleware.AuthMiddleware())
}
